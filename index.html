<!DOCTYPE html>
<html lang="">
  <head>
    <meta charset="UTF-8" />
    <title>NuStar</title>
    <link rel="icon" href="/favicon.ico" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"
    />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />
    <meta name="theme-color" content="#ffffff" />
    <meta name="format-detection" content="telephone=no" />
    <meta name="msapplication-tap-highlight" content="no" />
    <meta name="renderer" content="webkit" />
    <meta name="force-rendering" content="webkit" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta name="full-screen" content="yes" />
    <meta name="x5-fullscreen" content="true" />
    <meta name="360-fullscreen" content="true" />
    <meta name="screen-orientation" content="portrait" />
    <meta name="x5-orientation" content="portrait" />
    <meta name="x5-page-mode" content="app" />

    <!-- 客服库 -->
    <script src="https://cdn.aihelp.net/webchatv5/aihelp.js" async></script>
    <!-- Geetest 验证码库 - 异步加载，不阻塞页面 -->
    <script src="https://static.geetest.com/g5/gd.js" async></script>
    <script src="https://static.geetest.com/v4/gt4.js" async></script>

    <!-- 第三方登录 SDK -->
    <!-- Google Login SDK -->
    <script src="https://accounts.google.com/gsi/client" async defer></script>
    <!-- Facebook Login SDK -->
    <script
      async
      defer
      crossorigin="anonymous"
      src="https://connect.facebook.net/en_US/sdk.js"
    ></script>

    <!-- Facebook 初始化脚本 -->
    <script>
      window.fbAsyncInit = function () {
        FB.init({
          appId: "***************", // NuStar Lemoon ID
          cookie: true, // 启用 cookie 才能让 FB.getLoginStatus 生效
          xfbml: true,
          version: "v23.0",
        });
      };
    </script>

    <script>
      var coverSupport =
        "CSS" in window &&
        typeof CSS.supports === "function" &&
        (CSS.supports("top: env(a)") || CSS.supports("top: constant(a)"));
      document.write(
        '<meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0' +
          (coverSupport ? ", viewport-fit=cover" : "") +
          '" />'
      );
    </script>
  </head>
  <body>
    <div id="app"></div>
    <script type="module" src="/src/main.ts"></script>
  </body>
</html>

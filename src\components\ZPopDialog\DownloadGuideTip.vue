<template>
  <div v-if="showDownloadGuide" @click="handleDownload" class="download-guide-container">
    <div class="download-guide-content">
      <div v-if="canClose" class="close-btn" @click.stop="handleClose">
        <ZIcon type="icon-guanbi1" color="#fff" :size="14" />
      </div>
      <div class="app-info">
        <img
          :src="downloadConfig.icon ? getServerSideImageUrl(downloadConfig.icon) : defaultAppIcon"
          alt="App Icon"
          class="app-icon"
        />
        <div class="app-details">
          <AutoResizeText
            v-if="downloadConfig.slogan"
            :text="downloadConfig.slogan"
            :container-width="180"
            :container-height="50"
            :padding="0"
            :max-font-size="12"
            :min-font-size="8"
            :multi-line="true"
            :max-lines="2"
            font-weight="400"
            container-class="app-slogan"
          />
        </div>
      </div>
      <AutoResizeText
        :text="downloadConfig.button_text || 'Download'"
        :container-width="92"
        :container-height="36"
        :padding="10"
        :max-font-size="14"
        :min-font-size="10"
        container-class="download-btn"
      />
    </div>
  </div>
  <CopyLinkTip v-model="showDialog" :copy-url="currentUrl" />
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from "vue";
import { storeToRefs } from "pinia";
import { CHANEL_TYPE } from "@/utils/config/GlobalConstant";
import { useAutoPopMgrStore } from "@/stores/autoPopMgr";
import { useGlobalStore } from "@/stores";
import { MobileWindowManager } from "@/utils/managers/MobileWindowManager";
import { getServerSideImageUrl } from "@/utils/core/tools";
import CopyLinkTip from "@/components/ZPopDialog/CopyLinkTip.vue";
import AutoResizeText from "@/components/AutoResizeText/index.vue";

// 默认应用图标 - 使用favicon
const defaultAppIcon = "/favicon.ico";

// Store引用
const autoPopMgrStore = useAutoPopMgrStore();
const { downloadGuideConfig } = storeToRefs(autoPopMgrStore);
const globalStore = useGlobalStore();
// 本地状态
const showDownloadGuide = ref(false);
// CopyLinkTip 响应式数据
const showDialog = ref(false);
const currentUrl = ref("");
// 计算属性：下载配置
const downloadConfig = computed(() => {
  return downloadGuideConfig.value || {};
});

// 计算属性：是否可以关闭
const canClose = computed(() => {
  // close_type: 1-允许关闭, 2-不允许关闭
  return downloadConfig.value?.close_type !== 2;
});

// 检查是否应该显示下载引导
const shouldShowDownloadGuide = () => {
  return !!downloadGuideConfig.value?.download_url;
};

// 处理关闭
const handleClose = () => {
  // 只有在允许关闭时才能关闭
  if (canClose.value) {
    showDownloadGuide.value = false;
  }
};

// 处理下载
const handleDownload = () => {
  if (globalStore.channel === CHANEL_TYPE.MAYA) {
    // 显示copylink弹窗
    currentUrl.value = downloadConfig.value?.download_url ?? "";
    showDialog.value = true;
    return;
  }
  const downloadUrl = downloadConfig.value?.download_url;

  if (downloadUrl) {
    // 使用MobileWindowManager处理下载链接
    const success = MobileWindowManager.navigateToUrl(downloadUrl);
  }
};

// 初始化显示逻辑
const initDownloadGuide = async () => {
  // 先获取配置
  await autoPopMgrStore.getDownloadGuideConfig();

  // 如果后台配置启用，则显示
  if (shouldShowDownloadGuide()) {
    showDownloadGuide.value = true;
  }
};

// 组件挂载时初始化
onMounted(() => {
  initDownloadGuide();
});

// 暴露方法供外部调用
defineExpose({
  show: () => {
    showDownloadGuide.value = true;
  },
  hide: () => {
    showDownloadGuide.value = false;
  },
});
</script>

<style lang="scss" scoped>
.download-guide-container {
  position: fixed;
  bottom: 80px; /* 避免遮挡底部导航栏，TabBar高度54px + 安全区域 */
  left: 12px;
  right: 12px;
  z-index: 999; /* 低于弹窗组件的z-index */
  background: #ac1140;
  padding: 8px 12px;
  border-radius: 12px; /* 添加圆角 */
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  animation: slideUp 0.3s ease-out;
  font-family: "Inter";
  width: 94vw;
  cursor: pointer;
}

.download-guide-content {
  display: flex;
  align-items: center;
  gap: 12px;
  position: relative;
}

.close-btn {
  position: absolute;
  top: -13px;
  right: -18px;
  width: 20px;
  height: 20px;
  font-weight: 800;
  line-height: 20px;
  text-align: center;
  background: rgba(0, 0, 0, 0.8);
  border-radius: 20px;
}

.app-info {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.app-icon {
  width: 36px;
  height: 36px;
  border-radius: 12px;
  object-fit: cover;
}

.app-details {
  flex: 1;
  color: white;
}

.app-title {
  font-size: 14px;
  font-weight: 700;
  margin-bottom: 2px;
}

.app-description {
  font-size: 12px;
  font-weight: 500;
  line-height: 1.3;
}

.app-slogan {
  margin-top: 2px;
  text-align: left;
  color: white;
  /* AutoResizeText 组件会处理字体大小、换行和文本适配 */
}

.download-btn {
  background: #fff;
  color: #ac1140;
  width: 92px;
  height: 36px;
  border-radius: 100px;
  text-align: center;
  padding: 10px;
  font-weight: 700;
  /* AutoResizeText 组件会处理字体大小和布局 */
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}
</style>

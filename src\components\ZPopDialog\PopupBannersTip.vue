<template>
  <ZPopOverlay :show="showPopupBannersTip">
    <div v-if="!allLoaded" @click="handleSkipLoading" class="preContent">
      <ZIcon type="icon-tupianjiazaizhong" :size="100" color="#ccc"></ZIcon>
      <div class="close" @click="handleSkipLoading">
        <ZIcon type="icon-guanbi2" color="#fff" :size="30" />
      </div>
    </div>
    <div class="content" v-else>
      <!-- 全部加载完成后显示实际内容 -->
      <transition v-if="!firstImage" name="zoom" mode="out-in">
        <div :key="currentBannerId" class="img-container" @click="onClickBanner">
          <img
            :src="currentDisplayImage"
            class="img"
            :alt="curBannerInfo.name || 'Banner'"
            @error="handleImageError"
            @load="handleImageLoad"
            @loadstart="handleImageLoadStart"
          />
          <div v-if="imageLoading" class="loading-overlay">
            <ZIcon type="icon-tupianjiazaizhong" :size="40" color="#ccc" />
          </div>
        </div>
      </transition>
      <div v-else class="img-container" @click="onClickBanner">
        <img
          :src="currentDisplayImage"
          class="img"
          :alt="curBannerInfo.name || 'Banner'"
          @error="handleImageError"
          @load="handleImageLoad"
          @loadstart="handleImageLoadStart"
        />
        <div v-if="imageLoading" class="loading-overlay">
          <ZIcon type="icon-tupianjiazaizhong" :size="40" color="#ccc" />
        </div>
      </div>
      <div class="close" @click="handleClose">
        <ZIcon type="icon-guanbi2" color="#fff" :size="30" />
      </div>
    </div>
  </ZPopOverlay>
</template>

<script lang="ts" setup>
import { ref, watch, computed } from "vue";
import { storeToRefs } from "pinia";
import { AutoPopMgr } from "@/utils/AutoPopMgr";
import { useAutoPopMgrStore } from "@/stores/autoPopMgr";
import { useGlobalStore } from "@/stores/global";
import { useDepositStore } from "@/stores/deposit";
import { bannerStorageManager } from "@/utils/managers/BannerStorageManager";
import { MobileWindowManager } from "@/utils/managers/MobileWindowManager";
import router from "@/router";
import placeholderBannerBase64 from "@/assets/constants/homeBannerBase64";
import { jumpGame } from "@/utils/JumpGame";
import { jumpPromosItem } from "@/utils/JumpPromo";
import { popBannerJump } from "@/utils/PopBannerJump";

// 使用 Base64 编码的占位图替代文件路径
const defaultPlaceholder = placeholderBannerBase64;
const globalStore = useGlobalStore();
const depositStore = useDepositStore();
const autoPopMgrStore = useAutoPopMgrStore();
const { showPopupBannersTip, popBanners, userInfo, preloadedBannerImages, bannerImagesPreloaded } =
  storeToRefs(autoPopMgrStore);

const curBannerIdx = ref(0);
const curBannerInfo = ref<any>({ image: "", id: "" });
const firstImage = ref(true);
const hasInitialized = ref(false);
const imageLoading = ref(true);

// 记录当前用户ID，用于检测登录状态变化
const currentUserId = ref(userInfo.value?.user_id);

// 监听用户登录状态变化
watch(
  () => userInfo.value?.user_id,
  (newUserId, oldUserId) => {
    // 如果用户状态发生变化（从未登录到登录，或从登录到未登录）
    if (newUserId !== oldUserId && hasInitialized.value) {
      console.log("用户登录状态变化，重新初始化 Banner:", { oldUserId, newUserId });

      // 重置组件状态
      hasInitialized.value = false;
      curBannerIdx.value = 0;
      currentUserId.value = newUserId;
    }
  }
);

const allLoaded = computed(() => {
  // 修复逻辑：如果没有 banner 数据，应该直接关闭弹窗，而不是显示加载状态
  const preloaded = bannerImagesPreloaded.value;
  const hasData = popBanners.value?.length > 0;

  // 如果预加载完成但没有数据，说明没有可显示的 banner，应该关闭弹窗
  if (preloaded && !hasData) {
    console.log("预加载完成但没有 banner 数据，应该关闭弹窗");
    // 延迟关闭弹窗，避免闪烁
    setTimeout(() => {
      if (showPopupBannersTip.value) {
        showPopupBannersTip.value = false;
      }
    }, 100);
    return false;
  }

  const result = preloaded && hasData;
  return result;
});

const currentBannerId = computed(() => curBannerInfo.value.id || curBannerIdx.value.toString());

const currentDisplayImage = computed(() => {
  const bannerId = curBannerInfo.value.id;

  if (!bannerId) {
    return defaultPlaceholder;
  }

  // 使用 store 中预加载的图片
  if (preloadedBannerImages.value[bannerId]) {
    return preloadedBannerImages.value[bannerId];
  }

  // 如果没有预加载，使用默认占位图
  return defaultPlaceholder;
});

// 不再需要组件内部的预加载逻辑，统一使用 store 中的预加载结果
watch(
  [() => autoPopMgrStore.showPopupBannersTip, () => autoPopMgrStore.popBanners],
  ([show, data]) => {
    if (show && data?.length > 0 && !hasInitialized.value) {
      // 直接显示第一个 banner（因为 store 中已经过滤了不可显示的）
      const element = data[0] as any;
      curBannerIdx.value = 0;
      curBannerInfo.value = { ...element };

      // 更新显示次数
      bannerStorageManager.recordShow(element.id, element.show_count, userInfo.value?.user_id);
      hasInitialized.value = true;
    }
  },
  { immediate: true }
);

watch(curBannerInfo, () => {
  if (firstImage.value) {
    setTimeout(() => {
      firstImage.value = false;
    }, 0);
  }
});

// 跳过加载等待，直接显示内容
const handleSkipLoading = () => {
  // 强制设置为已加载状态，即使图片还没完全预加载完成
  // 这样用户可以跳过等待直接看到弹窗内容
  autoPopMgrStore.bannerImagesPreloaded = true;
};

const onClickBanner = () => {
  showPopupBannersTip.value = false;
  if (!curBannerInfo.value.jump_type) return;
  if (!globalStore.token) {
    router.push("/login");
    return;
  }
  switch (curBannerInfo.value.jump_type) {
    case 3:
      // none
      router.push("/promos");
      break;
    case 4:
      //这里跳转内置 网页
      router.push("/promo-webview?url=" + curBannerInfo.value.jump_url);
      break;
    case 5:
      //Outside URL
      MobileWindowManager.navigateToUrl(curBannerInfo.value.jump_url);
      break;
    case 6:
      //遗留问题
      //直接跳转活动详情页
      let activity_list = curBannerInfo.value?.activity_list || "";
      if (activity_list.length > 0) {
        console.log("curBannerInfo.value", curBannerInfo.value);
        popBannerJump(curBannerInfo.value);
      }
      break;
    case 7:
      // 直接跳转活动详情页
      let activity_id = curBannerInfo.value?.active_banner_detail_id || "";
      if (activity_id) {
        let filter_banner = autoPopMgrStore.banners.filter((item) => {
          return item.id == activity_id;
        });
        if (filter_banner.length > 0) {
          jumpPromosItem(filter_banner[0]);
        }
      }
      break;
    case 8:
      // 跳游戏
      let gid = curBannerInfo.value.gid || "";
      if (gid) {
        jumpGame({ id: gid });
      }
      break;
    case 9:
      // 跳全部游戏页面，Game type与Provider
      let game_type = curBannerInfo.value?.game_type || "";
      let provider = curBannerInfo.value?.provider || "[]";
      const query = {
        categoryId: game_type,
        providerIds: JSON.parse(provider).join(","),
      };
      router.push({
        path: "/game-categories",
        query,
      });
      break;
    case 10:
      //充值
      depositStore.openDialog();
      break;
    default:
      break;
  }
  // 点击详情之后 都不向下通知
  AutoPopMgr.resetAllPopups();
};

const handleClose = () => {
  curBannerIdx.value++;
  if (curBannerIdx.value < popBanners.value.length) {
    const element = popBanners.value[curBannerIdx.value] as any;
    curBannerInfo.value = element;
    firstImage.value = false;

    // 更新新 banner 的显示次数
    bannerStorageManager.recordShow(element.id, element.show_count, userInfo.value?.user_id);
  } else {
    showPopupBannersTip.value = false;
    AutoPopMgr.destroyCurrentPopup();
  }
};

// 处理图片加载开始
const handleImageLoadStart = () => {
  // 只有在图片没有预加载的情况下才显示加载状态
  const bannerId = curBannerInfo.value.id;
  const isPreloaded = preloadedBannerImages.value[bannerId];
  if (!isPreloaded) {
    imageLoading.value = true;
  }
};

// 处理图片加载完成
const handleImageLoad = () => {
  imageLoading.value = false;
};

// 处理图片加载错误
const handleImageError = (event: Event) => {
  imageLoading.value = false;
  const img = event.target as HTMLImageElement;
  if (img && img.src !== defaultPlaceholder) {
    img.src = defaultPlaceholder;
  }
};

// 不再需要 onMounted 中的预加载，统一使用 store 中的预加载结果
</script>

<style lang="scss" scoped>
.preContent {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.content {
  border-radius: 15px;
  box-sizing: border-box;
  position: relative;
  z-index: 9;

  .img-container {
    width: 70vw;
    height: 60vh;
    border-radius: 15px;
    overflow: hidden;
    // background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    background: transparent;
    position: relative;

    .img {
      width: 100%;
      height: 100%;
      object-fit: fill;
      display: block;
      transition: opacity 0.3s ease;

      /* 确保图片加载时不会闪烁 */
      &[src=""] {
        opacity: 0;
      }
    }

    .loading-overlay {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      // background: rgba(245, 247, 250, 0.8);
      background: transparent;
      backdrop-filter: blur(2px);
      transition: opacity 0.3s ease;
    }
  }

  .close {
    position: absolute;
    bottom: -50px;
    left: 50%;
    transform: translateX(-50%);
  }
}

/* 缩放过渡动画 */
.zoom-enter-active,
.zoom-leave-active {
  transition: transform 0.4s ease;
}

.zoom-enter-from,
.zoom-leave-to {
  transform: scale(0.1);
}

.zoom-enter-to,
.zoom-leave-from {
  transform: scale(1);
}
</style>

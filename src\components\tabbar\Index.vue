<script setup lang="ts">
import { computed, ref } from "vue";
import { useRouter, useRoute } from "vue-router";
import { useGlobalStore } from "@/stores/global";

import IconHome from "@/assets/icons/home-provider/tab_home.svg";
import IconHomeActive from "@/assets/icons/home-provider/tab_home_active.svg";
import IconPromos from "@/assets/icons/home-provider/tab_promos.svg";
import IconPromosActive from "@/assets/icons/home-provider/tab_promos_active.svg";
import IconNews from "@/assets/icons/home-provider/tab_news.svg";
import IconNewsActive from "@/assets/icons/home-provider/tab_news_active.svg";
import IconAccount from "@/assets/icons/home-provider/tab_account.svg";
import IconAccountActive from "@/assets/icons/home-provider/tab_account_active.svg";
import IconGames from "@/assets/icons/home-provider/tab_games.svg";
import TabBarBg from "@/assets/images/home/<USER>";

const router = useRouter();
const route = useRoute();
const globalStore = useGlobalStore();

// 定义 Tab 项接口
import type { ComputedRef } from "vue";
interface TabItem {
  name: string;
  path: string;
  icon?: any; // 存储 SVG 组件
  activeIcon?: any; // 存储激活态 SVG 组件
  showBadge?: ComputedRef<boolean>;
  isSpecial?: boolean;
}

// 使用工厂函数创建 Tab 项
const createTabItem = (config: TabItem): TabItem => ({
  ...config,
  showBadge: config.showBadge || computed(() => false),
});

// 可在视图中控制小红点 减少开销
const tabList = ref<TabItem[]>([
  createTabItem({
    name: "Home",
    path: "/",
    icon: IconHome,
    activeIcon: IconHomeActive,
    showBadge: computed(() => false),
  }),
  createTabItem({
    name: "Promos",
    path: "/promos",
    icon: IconPromos,
    activeIcon: IconPromosActive,
    showBadge: computed(() => false), // 暂时关闭 Promos 的小红点
  }),
  createTabItem({
    name: "Games",
    path: `/game-categories`,
    isSpecial: true,
  }),
  createTabItem({
    name: "News",
    path: "/news",
    icon: IconNews,
    activeIcon: IconNewsActive,
    showBadge: computed(() => globalStore.hasUnreadNews),
  }),
  createTabItem({
    name: "Account",
    path: "/account",
    icon: IconAccount,
    activeIcon: IconAccountActive,
    showBadge: computed(() => false),
  }),
]);

// 提取路由切换逻辑
const navigateTo = (tab: TabItem) => {
  if (tab.name === "Games") {
    router.push(tab.path);
  } else {
    router.replace(tab.path);
  }
};

// 检查当前路由是否激活
const isRouteActive = (path: string) => {
  return (
    route.path === path ||
    (path === "/" && route.path === "/home") ||
    (["/promos/0", "/promos/1"].includes(route.path) && path === "/promos")
  );
};
</script>

<template>
  <div class="tabbar">
    <div class="tab-bar safe-area-bottom">
      <!-- SVG 背景组件 -->
      <component :is="TabBarBg" class="tab-bar-bg" />
      <div
        v-for="item in tabList"
        :key="item.path"
        class="tab-item"
        :class="{
          active: isRouteActive(item.path),
          games: item.isSpecial,
        }"
        @click="navigateTo(item)"
      >
        <div class="icon-wrapper">
          <template v-if="item.isSpecial">
            <div class="icon-games">
              <component :is="IconGames" />
            </div>
          </template>
          <template v-else>
            <!-- 使用动态组件渲染 SVG -->
            <component :is="isRouteActive(item.path) ? item.activeIcon : item.icon" />
          </template>
          <span v-if="item.showBadge" class="badge"></span>
        </div>
        <span class="name">{{ item.name }}</span>
      </div>
    </div>
    <div class="foot-height"></div>
  </div>
</template>

<style scoped lang="scss">
.tabbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 10;
}

.foot-height {
  height: 10px;
  background-color: #fff;
  width: 100%;
  margin-top: -2px;
}

.icon-games {
  position: relative;
  left: 50%;
  top: -8px;
  width: 48px;
  height: 48px;
  transform: translateX(-50%);
  z-index: 2;
  border-radius: 50%;
  background-color: var(--red-color);
  display: flex;
  align-items: center;
  justify-content: center;
}

.tab-bar {
  height: 54px;
  display: flex;
  position: relative;
  background-color: transparent;
  width: 100%;
  min-width: 100%;
  /* 确保最小宽度 */
  /* 防止内容溢出 */

  .tab-bar-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;
    pointer-events: none;
    /* 确保不会阻挡点击事件 */

    /* 确保 SVG 完全填充容器并自适应 */
    svg {
      width: 100%;
      height: 100%;
      display: block;
      object-fit: fill;
      /* 强制填充整个容器 */
      object-position: center;
    }

    /* 针对不同的 SVG 组件实现方式 */
    :deep(svg) {
      width: 100% !important;
      height: 100% !important;
      display: block !important;
      min-width: 100%;
      max-width: none;
    }
  }

  .tab-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: end;
    color: #666;
    position: relative;
    z-index: 1;
    font-family: Inter;
    font-size: 10px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    cursor: pointer;
    /* 确保显示在背景 SVG 之上 */

    &.active {
      color: #222;
      font-weight: 500;
    }

    .icon-wrapper {
      position: relative;

      .badge {
        position: absolute;
        top: 4px;
        right: -3px;
        width: 8px;
        height: 8px;
        background: var(--danger-color);
        border-radius: 50%;
      }
    }
  }
}
</style>

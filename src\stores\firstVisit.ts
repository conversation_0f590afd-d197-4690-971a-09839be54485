import { defineStore } from "pinia";

const STORAGE_KEY = "visited-routes";

// 从 localStorage 加载已访问的路由
const loadVisitedRoutes = (): Record<string, boolean> => {
  try {
    const stored = localStorage.getItem(STORAGE_KEY);
    return stored ? JSON.parse(stored) : {};
  } catch (error) {
    console.warn("Failed to load visited routes from localStorage:", error);
    return {};
  }
};

// 保存已访问的路由到 localStorage
const saveVisitedRoutes = (visitedRoutes: Record<string, boolean>): void => {
  try {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(visitedRoutes));
  } catch (error) {
    console.warn("Failed to save visited routes to localStorage:", error);
  }
};

export const useFirstVisitStore = defineStore("firstVisit", {
  state: () => ({
    visitedRoutes: loadVisitedRoutes(),
  }),
  actions: {
    isFirstVisit(routePath: string): boolean {
      return !this.visitedRoutes[routePath];
    },
    markAsVisited(routePath: string): void {
      this.visitedRoutes[routePath] = true;
      saveVisitedRoutes(this.visitedRoutes);
    },
    clearVisitedRoutes(): void {
      this.visitedRoutes = {};
      saveVisitedRoutes(this.visitedRoutes);
    },
  },
});

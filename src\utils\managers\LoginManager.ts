/**
 * 登录管理器
 * 负责处理应用启动时的登录逻辑，包括自动登录、错误处理、页面跳转等
 *
 * 主要功能：
 * - 自动登录和令牌验证
 * - 统一的错误处理和用户提示
 * - 账户锁定提示
 * - 页面跳转管理
 *
 * @version 2.0.0
 * <AUTHOR>
 */

import { useGlobalStore } from "@/stores/global";
import { useAutoPopMgrStore } from "@/stores/autoPopMgr";
import { playerLogin, playerDetail } from "@/api/user";
import { getBanners } from "@/api/games";
import { ALL_APP_SOURCE_CONFIG } from "@/utils/config/Config";
import { CHANEL_TYPE, MAINTENANCETIPCODE } from "@/utils/config/GlobalConstant";
import { setLocalStorage, getLocalStorage } from "@/utils/core/Storage";
import { getGlobalDialog, getGlobalActionSheet } from "@/enter/vant";
import { jumpPromo } from "@/utils/JumpPromo";
import { useClipboard } from "@/hooks/useClipboard";
import { showToast } from "vant";
import router from "@/router";
import { serviceMgr } from "@/utils/ServiceMgr";
import { AutoPopMgr } from "@/utils/AutoPopMgr";
import { useGameStore } from "@/stores/game";
import { redirectToLoginWithCurrentParams } from "@/utils/managers/RedirectManager";
import enTranslations from "@/utils/I18n/en.json";
import { sleep } from "@/utils/core/tools";

export const SESSION_ID = "kSessionId";

// 类型定义
interface ApiResponse<T = any> {
  code: number;
  msg: string;
  data: T;
}

interface LockTipData {
  code?: number;
  msg?: string;
  data?: {
    customer_email?: string;
    lock_time?: number;
    [key: string]: any;
  };
}

const REDIRECT_PAGE = {
  game: "game", //type 游戏 分类 带 id
  promos: "promos", //点击广告条 带的参数
  news: "news", //点击广告条 带的参数
  vip: "vip", //点击vip
  all: "all", //更多游戏
  service: "service", //客服
  bet: "bet", //go bet 点击之后的逻辑
  banner: "banner", //banner跳转
  pay: "pay", //支付后的回掉
};

export interface LoginParams {
  login_type: string;
  token: string;
  session_id?: string;
  g_auth_code?: string;
  maya_auth_code?: string;
  appChannel: string;
  appSource: string;
  appVersion: string;
  appPackageName: string;
  registration_channel: string;
}

export class LoginManager {
  private _autologin_times = 0;
  /**
   * 获取 GlobalStore 实例
   */
  private getGlobalStore() {
    return useGlobalStore();
  }
  private getAutoPopMgrStore() {
    return useAutoPopMgrStore();
  }
  private $dialog() {
    return getGlobalDialog();
  }

  public re_page = ""; //计算出 显示那个页面
  public re_page_params = ""; //带的第二个参数 如果有就用 这个目前就 首页的 游戏类型使用到
  public re_page_params3 = ""; //带的第三个参数,充值金额

  /**
   * 显示账户锁定弹窗
   */
  public showLock(args?: LockTipData) {
    const { copy } = useClipboard();
    const { data, msg, code } = args || {};
    const content = msg || "";
    const email = data?.customer_email || "";

    // 构建更美观的内容
    const lockContent = `
      <div style="text-align: center; padding: 10px;">
        <div style="font-size: 14px; color: #666; line-height: 1.5; margin-bottom: 20px;">
          ${content}
        </div>
        <div style="border-radius: 8px; padding: 12px; margin-bottom: 16px;">
          <div style="font-size: 14px; color: #ac1140; font-weight: 600; word-break: break-all;">
            ${email}
          </div>
        </div>
      </div>
    `;

    getGlobalActionSheet()({
      title: "Account Locked",
      content: lockContent,
      showCancelButton: false,
      confirmText: "Copy Email",
      onConfirm: async () => {
        try {
          await copy(email);
          showToast("Email copied to clipboard!");
        } catch (error) {
          console.error("Failed to copy email:", error);
          showToast("Failed to copy email");
        }
      },
    });
  }

  /**
   * 显示通用错误对话框
   */
  private showErrorDialog(options: {
    message: string;
    confirmText?: string;
    showCancelButton?: boolean;
    showClose?: boolean;
    onConfirm?: () => void;
  }) {
    const {
      message,
      confirmText = "OK",
      showCancelButton = false,
      showClose = false,
      onConfirm,
    } = options;

    this.$dialog()({
      message,
      confirmText,
      showCancelButton,
      showClose,
      onConfirm,
    });
  }

  private showCustomerServiceDialog(code: number) {
    this.showErrorDialog({
      message: enTranslations[`php_code_${code}`] || `Error code: ${code}`,
      confirmText: "Customer service",
      showClose: true,
    });
  }

  /**
   * 重定向到登录页面，携带当前页面参数
   */
  redirectToLogin() {
    redirectToLoginWithCurrentParams();
  }

  async jumpPage() {
    const urlParams = new URLSearchParams(window.location.search);
    this.re_page = urlParams.get("type") || urlParams.get("redirect_type") || "home";
    this.re_page_params = urlParams.get("id") || urlParams.get("redirect_id") || "";
    this.re_page_params3 = urlParams.get("amount") || urlParams.get("redirect_amount") || ""; // 充值金额

    // 主要是兼容cocos 逻辑
    if (this.re_page_params === "pending") {
      // 打开transaction页面
      router.replace(`/account/transactions/Deposit`);
      return;
    }
    if (this.re_page_params3) {
      // 稍后打开充值页
      router.replace(`/account`);
      return;
    }

    switch (this.re_page) {
      case REDIRECT_PAGE.game:
        // 首页
        router.replace(`/home`);
        break;
      case REDIRECT_PAGE.service:
        //稍后跳到 客服页面
        this.re_page_params = "service";
        router.replace(`/account`);
        break;
      case REDIRECT_PAGE.all:
        // 更多游戏
        router.replace(`/game-categories?categoryId=${this.re_page_params}`);
        break;
      case REDIRECT_PAGE.promos:
        router.replace(`/promos`);
        break;
      case REDIRECT_PAGE.vip:
        router.replace("/account");
        break;
      case REDIRECT_PAGE.news:
        router.replace("/news");
        break;
      //
      case REDIRECT_PAGE.bet:
        useGameStore().clickBetNow();
        break;
      case REDIRECT_PAGE.banner:
        if (this.re_page_params) {
          const res = (await getBanners({})) as unknown as { banner: any[] };
          if (res?.banner?.length > 0) {
            const banner = res.banner.find((r) => r.id == this.re_page_params);
            if (banner) {
              //是否跳转promo页面 home_page_jump 1:promo 2:jump type mode
              if (banner.home_page_jump == 1) {
                router.replace("/promos");
              } else if (banner.home_page_jump == 2) {
                jumpPromo(banner, "banner");
              } else {
                router.replace("/promos");
              }
            }
          }
        } else {
          router.replace("/promos");
        }
        break;
      case REDIRECT_PAGE.pay:
        if (this.re_page_params == "success") {
          useGameStore().clickBetNow();
        } else {
          router.replace("/account");
        }
        break;

      default:
        router.replace("/home");
        break;
    }
  }

  public async setChannel() {
    const urlParams = new URLSearchParams(window.location.search);
    const debug_gcash = urlParams.get("debug_gcash");
    const debug_maya = urlParams.get("debug_maya");
    const debug_web_login = urlParams.get("debug_web_login");
    const globalStore = this.getGlobalStore();
    if (debug_gcash) {
      globalStore.setChannel(CHANEL_TYPE.G_CASH);
    } else if (debug_maya) {
      globalStore.setChannel(CHANEL_TYPE.MAYA);
    } else if (debug_web_login) {
      globalStore.setChannel(CHANEL_TYPE.WEB);
    }
  }

  async preLogin() {
    // 模拟渠道
    const urlParams = new URLSearchParams(window.location.search);
    const debug_gcash = urlParams.get("debug_gcash");
    const debug_maya = urlParams.get("debug_maya");

    const gcashCode = urlParams.get("g_auth_code");
    const mayaCode = urlParams.get("sessionId");

    const globalStore = this.getGlobalStore();
    const token = globalStore.token;
    if (gcashCode || mayaCode) {
      await this.autoLogin(true);
      return;
    }
    if (debug_gcash || globalStore.channel === CHANEL_TYPE.G_CASH) {
      globalStore.setChannel(CHANEL_TYPE.G_CASH);
      if (token) {
        this.jumpPage();
      } else {
        this.redirectToLogin();
      }
      return;
    } else if (debug_maya || globalStore.channel === CHANEL_TYPE.MAYA) {
      globalStore.setChannel(CHANEL_TYPE.MAYA);
      if (token) {
        this.jumpPage();
      } else {
        this.redirectToLogin();
      }
      return;
    }
  }

  /**
   * 构建登录基础参数
   */
  private buildBaseParams(): Record<string, any> {
    return {
      appChannel: ALL_APP_SOURCE_CONFIG.channel,
      appSource: String(ALL_APP_SOURCE_CONFIG.source || ""),
      appVersion: ALL_APP_SOURCE_CONFIG.app_version,
      appPackageName: ALL_APP_SOURCE_CONFIG.appPackageName,
      buds: 64,
    };
  }

  /**
   * 处理登录错误
   */
  private handleLoginError(errorOrResponse: any): void {
    // 如果是网络错误或其他异常
    if (!errorOrResponse.code) {
      console.error("登录失败:", errorOrResponse);
      this.showErrorDialog({
        message: "Network error, please check your connection and try again",
        showCancelButton: false,
        confirmText: "Retry",
        onConfirm: () => {
          const globalStore = this.getGlobalStore();
          globalStore.clearStore();
          this.setChannel();
          window.location.reload();
        },
      });
      return;
    }

    // 处理 API 响应错误
    const { code, msg, data } = errorOrResponse as ApiResponse;

    // 处理不同的错误码
    switch (code) {
      case MAINTENANCETIPCODE:
        router.push("/system/maintenance");
        break;

      case 1:
        this.showErrorDialog({
          message: msg || "Login failed",
          showCancelButton: false,
        });
        break;

      case 103034:
        this.showErrorDialog({
          message: enTranslations[`php_code_${code}`] || `Error code: ${code}`,
          confirmText: "Customer service",
          showCancelButton: false,
        });
        break;

      case 102024:
      case 102006:
      case 102042:
      case 102045:
        this.showCustomerServiceDialog(code);
        break;

      case 102008:
      case 101013:
      case 101014:
        this.showLock({ code, msg, data });
        break;

      case 100010:
        this.showErrorDialog({
          message: enTranslations[`php_code_${code}`] || `Error code: ${code}`,
          showCancelButton: false,
        });
        break;

      default:
        this.showErrorDialog({
          message: msg || `Unknown error: ${code}`,
          showCancelButton: false,
        });
        break;
    }
  }

  /**
   * 执行手动登录（用于登录页面）
   */
  async executeLogin(payload: Record<string, any>): Promise<void> {
    try {
      const baseParams = {
        ...this.buildBaseParams(),
        ...payload,
      };

      // 执行登录请求
      const response = (await playerLogin(baseParams as any)) as unknown as ApiResponse;
      const { code, msg, data } = response;

      if (code === 200 || code === 0) {
        this.handleLoginSuccess(data);
        console.log("登录成功");
        return;
      }

      // 处理登录失败
      this.handleLoginError(response);
    } catch (error) {
      console.error("Login failed:", error);
      throw error;
    }
  }

  /**
   * 执行自动登录流程（用于应用启动时）
   */
  async autoLogin(has_expire = false): Promise<void> {
    try {
      // 构建基础参数
      let baseParams = this.buildBaseParams();
      const globalStore = this.getGlobalStore();
      const urlParams = new URLSearchParams(window.location.search);
      const gcashCode = urlParams.get("g_auth_code");
      const mayaCode = urlParams.get("sessionId");
      const token = globalStore.token;

      if (gcashCode) {
        baseParams["login_type"] = "gcash_token";
        baseParams["gcash_auth_code"] = gcashCode;
        baseParams["token"] = gcashCode;
        baseParams["registration_channel"] = CHANEL_TYPE.G_CASH;
        baseParams["appChannel"] = CHANEL_TYPE.G_CASH;

        globalStore.setChannel(CHANEL_TYPE.G_CASH);

        if (!has_expire && token && token != "") {
          this.autologin_token(token);
          return;
        }
        if (!has_expire || this._autologin_times >= 2) {
          //token和令牌都过期后的逻辑
          this.$dialog()({
            title: "Tips",
            message: "Since you have not operated on for a long time, please log in again.",
            confirmText: "Done",
            showCancelButton: false,
            onConfirm: async () => {
              router.replace("/system/maintenance");
            },
          });
          return;
        }
        this._autologin_times++;
      } else if (mayaCode) {
        baseParams["login_type"] = "maya";
        baseParams["session_id"] = mayaCode;
        baseParams["registration_channel"] = CHANEL_TYPE.MAYA;
        baseParams["appChannel"] = CHANEL_TYPE.MAYA;
        globalStore.setChannel(CHANEL_TYPE.MAYA);
      } else if (globalStore.channel === CHANEL_TYPE.WEB) {
        // Web 渠道的自动登录逻辑
        return;
      } else if (!!token) {
        baseParams["login_type"] = "token";
        baseParams["token"] = token;
      }

      // 执行登录请求
      const response = (await playerLogin(baseParams as any)) as unknown as ApiResponse;
      const { code, data } = response;

      if (code === 200 || code === 0) {
        this.handleLoginSuccess(data);
        console.log("自动登录成功");
        return;
      }

      // 处理特殊的自动登录错误码
      if (code === 100010) {
        // 一次性令牌过期，重试自动登录
        this.autoLogin();
        return;
      }

      // 其他错误使用通用错误处理
      this.handleLoginError(response);
    } catch (error) {
      console.error("自动登录失败:", error);
      // 对于自动登录失败，通常不需要显示错误提示
      // 可以在这里添加日志记录或其他处理逻辑
    }
  }

  async autologin_token(token: string) {
    const globalStore = this.getGlobalStore();
    try {
      const response = (await playerDetail({ token })) as unknown as ApiResponse;
      const { code, data, msg } = response;

      if (code === 200 || code === 0) {
        this.handleLoginSuccess(data);
        return;
      }

      // 处理不同的错误码
      switch (code) {
        case MAINTENANCETIPCODE:
          router.push("/system/maintenance");
          break;

        case 401:
        case 100010:
          // setLocalStorage(SESSION_ID, "");
          globalStore.token = "";
          await this.autoLogin(true);
          break;

        case 1:
          this.showErrorDialog({
            message: msg || "Login failed",
            showCancelButton: false,
          });
          break;

        case 103034:
          this.showErrorDialog({
            message: enTranslations[`php_code_${code}`] || `Error code: ${code}`,
            showCancelButton: false,
          });
          break;

        case 102024:
        case 102006:
        case 102042:
        case 102045:
          this.showCustomerServiceDialog(code);
          break;

        case 102008:
        case 101013:
        case 101014:
          this.showLock({ code, msg, data });
          break;

        default:
          this.showCustomerServiceDialog(code);
          break;
      }
    } catch (error) {
      console.error("Auto login token error:", error);
      this.showErrorDialog({
        message: "Network error, please try again",
        showCancelButton: false,
      });
    }
  }
  /**
   * 处理登录成功
   */
  private handleLoginSuccess(res: any): void {
    if (res) {
      const globalStore = this.getGlobalStore();
      globalStore.setLoginInfo(res);
      globalStore.getBalance();
      // 客服系统也登陆一下
      serviceMgr.instance.login();
      // 重置弹窗开发
      const autoPopMgrStore = this.getAutoPopMgrStore();
      autoPopMgrStore.hasPop = false;
      AutoPopMgr.resetAllPopups();

      // 登录成功后立即设置 Tip21Old 标记，防止误弹
      window["showTip21Old"] = true;
      autoPopMgrStore.showTips21Tip = false;
    }
    this.jumpPage();
  }

  /**
   * 检查是否需要登录
   */
  needsLogin(): boolean {
    const urlParams = new URLSearchParams(window.location.search);
    const gcashCode = urlParams.get("g_auth_code");
    const mayaCode = urlParams.get("maya_auth_code");
    const globalStore = this.getGlobalStore();

    return !!(gcashCode || mayaCode || globalStore.token);
  }

  /**
   * 清理 URL 参数
   */
  cleanupUrlParams(): void {
    const url = new URL(window.location.href);
    url.searchParams.delete("g_auth_code");
    url.searchParams.delete("maya_auth_code");
    // 更新 URL 但不刷新页面
    window.history.replaceState({}, document.title, url.toString());
  }
}

// 导出单例实例
export const loginManager = new LoginManager();
